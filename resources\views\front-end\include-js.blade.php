<script src="{{ asset('assets/front-end/assets/js/core/popper.js') }}"></script>
<script src="{{ asset('assets/front-end/assets/js/core/bootstrap.min.js') }}" type="text/javascript"></script>
<script src="{{ asset('assets/front-end/assets/js/soft-design-system.js') }}" type="text/javascript"></script>
<script src="{{ asset('assets/front-end/assets/js/plugins/countup.min.js') }}" type="text/javascript"></script>
<script src="{{ asset('assets/front-end/assets/js/plugins/flatpickr.min.js') }}"></script>
<script src="{{ 'assets/front-end/assets/js/plugins/typedjs.js' }}"></script>
<script src="{{ asset('assets/front-end/assets/js/custom.js') }}"></script>
<script src="{{ asset('assets/js/tinymce.min.js') }}"></script>
<script src="{{ asset('assets/js/tinymce-jquery.min.js') }}"></script>

<!-- Date picker -->
<script src="{{ asset('assets/js/moment.min.js') }}"></script>

<script src="{{ asset('assets/js/daterangepicker.js') }}"></script>

<script src="{{ asset('assets/lightbox/lightbox.min.js') }}"></script>

<script src="{{ asset('assets/js/dropzone.min.js') }}"></script>
<script>
    var csrf_token = '{{ csrf_token() }}';
    var js_date_format = '{{ $js_date_format ?? 'YYYY-MM-DD' }}';
</script>


<script src="{{ asset('assets/front-end/assets/js/loopple/loopple.js') }}"></script>
<script src="{{ asset('assets/js/toastr.min.js') }}"></script>
<script src="{{ asset('assets/front-end/assets/js/plugins/lottie.js') }}"></script>

<script>
    var toastTimeOut = {{ isset($general_settings['toast_time_out']) ? $general_settings['toast_time_out'] : 5 }};
    var toastPosition = "{{ isset($general_settings['toast_position']) ? $general_settings['toast_position'] : 'toast-top-right' }}";
</script>
<script src="{{ asset('assets/js/custom.js') }}"></script>

@if (session()->has('message'))
<script>
    toastr.options = {
        "positionClass": toastPosition,
        "showDuration": "300",
        "hideDuration": "1000",
        "timeOut": parseFloat(toastTimeOut) * 1000,
        "progressBar": true,
        "extendedTimeOut": "1000",
        "closeButton": true
    };
    toastr.success('{{ session('message') }}', 'Success');
</script>
@elseif(session()->has('error'))
<script>
    toastr.options = {
        "positionClass": toastPosition,
        "showDuration": "300",
        "hideDuration": "1000",
        "timeOut": parseFloat(toastTimeOut) * 1000,
        "progressBar": true,
        "extendedTimeOut": "1000",
        "closeButton": true
    };
    toastr.error('{{ session('error') }}', 'Error');
</script>
@endif

<!-- ===== NESTKO CHATBOT WIDGET JAVASCRIPT ===== -->
<script>
// ===== LARAVEL CSRF TOKEN =====
const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';

// ===== NESTKO KNOWLEDGE BASE =====
const NESTKO_KNOWLEDGE = `
# NestKo Project Management Platform

## Overview
NestKo is your all-in-one solution for smarter project management. We simplify project and task management with powerful tools to organize tasks, collaborate in real-time, and track progress — all in one place. Our user-friendly interface makes it easy for anyone to get started, regardless of technical expertise.

## Company Mission
We are passionate about empowering teams to achieve peak productivity. NestKo makes project management simple and effective, helping teams collaborate, track tasks, and manage projects efficiently.

## Main Benefits
1. **Manage Projects with Ease** - Stay on top of deadlines, goals, and timelines with NestKo's smart project tools
2. **Simplify Task Management** - Assign, monitor, and complete tasks effectively with clear accountability
3. **Enhance Team Collaboration** - NestKo connects your team through messaging, meetings, and shared progress
4. **Powerful Reporting** - Make better decisions with real-time insights and custom reports

## Core Features - Every Feature Your Team Needs To Complete Work Faster

### Project Management
- Manage all your company projects from one dashboard
- Intuitive dashboards with customizable views
- Project templates and workflows
- Milestone tracking and deadline management
- Progress visualization and reporting
- Simple and intuitive project management with no learning curves
- Visualize projects with intuitive dashboards and customizable views

### Task Management & Organization
- Create and assign tasks to your team effectively
- Task priorities and deadlines
- Subtask creation and dependencies
- Flexible workspace system with custom statuses (To Do, In Progress, Completed)
- Task templates for recurring work
- Effective task organization with workspaces and statuses
- Break down complex projects into manageable tasks and subtasks
- Prioritize effectively by highlighting critical tasks

### Team Collaboration & Communication
- Instant messaging between team members
- Real-time team communication
- Comments, mentions, and discussions
- Activity feeds and notifications
- @mentions and notification system
- Foster seamless collaboration with built-in communication tools
- Stay on the same page with real-time task updates and activity feeds
- Centralize all project-related information, documents, and files in one accessible location
- Break down silos with improved team collaboration and communication

### User Management
- Handle team roles, permissions, and access easily
- User authentication and access control
- Role-based permissions
- Activity logging and audit trails

### Client Management
- Manage client records and communication
- Client project tracking
- Communication history

### Meetings & Virtual Collaboration
- Schedule and manage virtual meetings
- Virtual meeting scheduling and management
- Video conferencing integration
- Team announcements and updates

### Financial Management
- Track expenses and handle company finances
- Expense tracking and reporting
- Budget management and forecasting
- Invoice creation and management
- Financial dashboard and analytics
- Generate and distribute employee payslips
- Payroll management and employee payment tracking

### Productivity & Efficiency Features
- Automate repetitive tasks to free up valuable time
- Minimize distractions with centralized task management
- Built-in time tracking and milestone management
- Progress reporting and deadline management
- Meet deadlines with confidence

### Language & Accessibility
- Use the app in multiple languages
- Multi-language support for global teams

### Security & Compliance
- Enterprise-grade security features
- Data encryption and secure access
- Regular security updates and monitoring
- Compliance with industry standards

## Key Features in Detail

### Streamline Your Projects
Take control of your projects and boost team productivity with NestKo, the all-in-one project management and task management solution. Our cloud-based platform empowers you to effortlessly organize projects, collaborate with your team, and track progress – all in one place.

### Effortless Organization
NestKo provides a centralized hub to create, manage, and track all your projects. Say goodbye to scattered tasks and missed deadlines – our intuitive interface keeps everything organized and accessible.

### Seamless Collaboration
Foster a collaborative work environment with NestKo. Assign tasks, share files, and communicate effectively with your team in real-time. Ensure everyone is on the same page and working towards a common goal.

### Visualize Project Health
Get insightful dashboards and reports to monitor project performance and identify areas for improvement.

## About Us - Simplifying Project & Task Management
NestKo is dedicated to simplifying project and task management for teams worldwide. We believe in:

1. **Managing Projects Efficiently** - Simplify your workflow with powerful tools to organize tasks, collaborate in real-time, and track progress — all in one place.

2. **Assign and Monitor Tasks** - Easily assign tasks to team members, set priorities, and track progress in real-time to ensure nothing falls through the cracks.

3. **Enhance Collaboration** - Connect your team with real-time communication, shared tasks, and seamless file sharing to boost teamwork and productivity.

## Frequently Asked Questions

### What are the key features of a project management system?
Key features typically include task management, team collaboration, project planning and scheduling, time tracking, file sharing, reporting and analytics, and integration with other tools.

### Can I handle multiple projects simultaneously?
Yes, most project management systems are designed to support the management of multiple projects concurrently. They typically provide features for organizing projects into separate workspaces or folders, allowing teams to easily switch between projects.

### How do I choose the right project management system?
When selecting a project management system, consider factors such as your team's size and requirements, the complexity of your projects, ease of use, scalability, customization options, pricing, customer support, and compatibility with existing tools and workflows. It's also helpful to try out different systems through free trials or demos to evaluate their suitability for your needs.

## Pricing Plans

### Free Plan
- Up to 5 users
- 3 projects
- Basic task management
- 1GB storage
- Email support

### Professional Plan ($9.99/month per user)
- Unlimited users
- Unlimited projects
- Advanced task management
- 100GB storage
- Priority support
- Time tracking
- Custom fields
- Advanced reporting

### Enterprise Plan ($19.99/month per user)
- Everything in Professional
- Advanced security features
- Custom integrations
- Dedicated account manager
- SLA guarantee
- Advanced analytics
- White-label options

## Getting Started
1. Sign up for a free account or start your free trial
2. Create your first project
3. Invite team members
4. Start organizing tasks and collaborating
5. Track progress and meet deadlines

## Contact Us & Support
Have questions or need support? Reach out to us!
- 24/7 customer support available
- Comprehensive documentation and tutorials
- Community forums for user discussions
- Regular webinars and training sessions
- Email support for technical assistance
- Contact form available on our website
- Live chat support

## Integration Capabilities
- Slack integration for seamless communication
- Google Workspace integration
- Microsoft Office 365 compatibility
- Zapier connections for workflow automation
- API access for custom integrations
- Popular tools and services integration

## Mobile & Accessibility
- iOS app available on App Store
- Android app available on Google Play
- Mobile-responsive design for work on any device
- Offline capability for mobile apps
- Cross-platform compatibility

## Why Choose NestKo?
- User-friendly interface with minimal learning curve
- Comprehensive feature set for complete project management
- Scalable solution that grows with your team
- Regular updates and new feature releases
- Strong focus on team collaboration and productivity
- Reliable cloud-based platform with 99.9% uptime
- Integration with popular tools and services
- No more learning curves - easy for anyone to get started
- Visualize projects with intuitive dashboards and customizable views
- Competitive pricing with flexible plans
- Proven track record with thousands of satisfied customers
`;

// ===== OPENROUTER AI INTEGRATION =====
const OPENROUTER_CONFIG = {
    API_KEY: '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',
    BASE_URL: 'https://openrouter.ai/api/v1',
    MODEL: 'z-ai/glm-4.5-air:free'
};

// ===== WIDGET STATE VARIABLES =====
let isWidgetOpen = false;
let currentScreen = 'welcome';
let messages = [];
let sessionId = '';
let isTyping = false;
let conversationHistory = [];

// ===== EMOJI DATA =====
const emojis = [
    "😀", "😃", "😄", "😁", "😆", "😅", "😂", "🤣", "😊", "😇", "🙂", "🙃", "😉", "😌", "😍", "🥰",
    "😘", "😗", "😙", "😚", "😋", "😛", "😝", "😜", "🤪", "🤨", "🧐", "🤓", "😎", "🤩", "🥳", "😏",
    "👋", "🤚", "🖐️", "✋", "🖖", "👌", "🤏", "✌️", "🤞", "🤟", "🤘", "🤙", "👈", "👉", "👆", "👇",
    "☝️", "👍", "👎", "👊", "✊", "🤛", "🤜", "👏", "🙌", "👐", "🤲", "🤝", "🙏", "❤️", "🧡", "💛",
    "💚", "💙", "💜", "🖤", "🤍", "🤎", "💔", "❣️", "💕", "💞", "💓", "💗", "💖", "💘", "💝", "💟"
];

// ===== FAQ QUICK ACTIONS =====
const nestkoFAQs = [
    { text: "💼 Project Management", query: "How does NestKo help with project management?" },
    { text: "💰 Pricing Plans", query: "What are NestKo's pricing plans and features?" },
    { text: "🚀 Getting Started", query: "How do I get started with NestKo?" },
    { text: "👥 Team Collaboration", query: "How does team collaboration work in NestKo?" },
    { text: "📊 Reports & Analytics", query: "What reporting and analytics features does NestKo offer?" },
    { text: "Just browsing 👀", query: "Just browsing, show me what NestKo can do" }
];

// ===== WIDGET CONTROL FUNCTIONS =====
function toggleWidget() {
    if (isWidgetOpen) {
        minimizeWidget();
    } else {
        openWidget();
    }
}

function openWidget() {
    const trigger = document.getElementById('widget-trigger');
    const main = document.getElementById('widget-main');

    // Smooth transition
    trigger.style.opacity = '0';
    trigger.style.transform = 'scale(0.8)';

    setTimeout(() => {
        trigger.style.display = 'none';
        main.style.display = 'flex';
        main.style.opacity = '0';
        main.style.transform = 'scale(0.9) translateY(20px)';

        // Animate in
        requestAnimationFrame(() => {
            main.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
            main.style.opacity = '1';
            main.style.transform = 'scale(1) translateY(0)';
        });
    }, 200);

    isWidgetOpen = true;

    if (!sessionId) {
        createSession();
    }
}

function minimizeWidget() {
    const trigger = document.getElementById('widget-trigger');
    const main = document.getElementById('widget-main');

    // Smooth transition out
    main.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
    main.style.opacity = '0';
    main.style.transform = 'scale(0.9) translateY(20px)';

    setTimeout(() => {
        main.style.display = 'none';
        trigger.style.display = 'block';
        trigger.style.opacity = '0';
        trigger.style.transform = 'scale(0.8)';

        // Animate trigger back in
        requestAnimationFrame(() => {
            trigger.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
            trigger.style.opacity = '1';
            trigger.style.transform = 'scale(1)';
        });
    }, 300);

    isWidgetOpen = false;
}

function closeWidget() {
    minimizeWidget();
    currentScreen = 'welcome';
    showWelcomeScreen();
}

function startChat() {
    currentScreen = 'chat';
    showChatScreen();

    addMessage('assistant', "Hello there! 👋 It's nice to meet you!");
    addMessage('assistant', "What brings you here today? Please use the navigation below or ask me anything about NestKo's project management features! 🚀");

    showFAQButtons();
}

function newChat() {
    messages = [];
    conversationHistory = [];
    clearMessages();
    startChat();
}

function showWelcomeScreen() {
    document.getElementById('welcome-screen').style.display = 'flex';
    document.getElementById('chat-screen').style.display = 'none';
}

function showChatScreen() {
    document.getElementById('welcome-screen').style.display = 'none';
    document.getElementById('chat-screen').style.display = 'flex';
}

// ===== SESSION MANAGEMENT =====
function createSession() {
    sessionId = 'laravel-session-' + Math.random().toString(36).substr(2, 9) + '-' + Date.now();
}

// ===== MESSAGE HANDLING =====
function addMessage(role, content, confidence = null, suggestions = null) {
    const message = {
        id: 'msg-' + Date.now() + '-' + Math.random().toString(36).substr(2, 5),
        role: role,
        content: content,
        timestamp: new Date(),
        confidence: confidence,
        suggestions: suggestions
    };

    messages.push(message);
    renderMessage(message);
    scrollToBottom();

    conversationHistory.push({
        role: role,
        content: content
    });

    if (conversationHistory.length > 10) {
        conversationHistory = conversationHistory.slice(-10);
    }
}

function renderMessage(message) {
    const messagesContainer = document.getElementById('messages-container');
    const messageElement = document.createElement('div');

    if (message.role === 'user') {
        messageElement.innerHTML = `
            <div class="flex items-start space-x-3 justify-end" style="display: flex; align-items: flex-start; gap: 0.75rem; justify-content: flex-end;">
                <div class="chat-bubble-user">
                    <p class="text-sm" style="font-size: 0.875rem; margin: 0;">${escapeHtml(message.content)}</p>
                </div>
                <div class="chat-avatar chat-avatar-user">
                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24" style="width: 1rem; height: 1rem; color: white;">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                </div>
            </div>
        `;
    } else {
        messageElement.innerHTML = `
            <div class="flex items-start space-x-3" style="display: flex; align-items: flex-start; gap: 0.75rem;">
                <div class="chat-avatar chat-avatar-assistant">
                    <div class="chat-avatar-inner">
                        <span>NK</span>
                    </div>
                </div>
                <div class="chat-bubble-assistant">
                    <p class="text-sm" style="font-size: 0.875rem; margin: 0;">${escapeHtml(message.content)}</p>
                    ${message.confidence ? `<div class="text-xs text-gray-500 mt-1" style="font-size: 0.75rem; color: #6b7280; margin-top: 0.25rem;">Confidence: ${Math.round(message.confidence * 100)}%</div>` : ''}
                </div>
            </div>
        `;
    }

    messagesContainer.appendChild(messageElement);
}

function clearMessages() {
    document.getElementById('messages-container').innerHTML = '';
}

function scrollToBottom() {
    const container = document.getElementById('messages-container');
    container.scrollTop = container.scrollHeight;
}

function showFAQButtons() {
    const messagesContainer = document.getElementById('messages-container');
    const faqElement = document.createElement('div');
    faqElement.className = 'flex flex-wrap gap-2 ml-11';
    faqElement.style.cssText = 'display: flex; flex-wrap: wrap; gap: 0.5rem; margin-left: 2.75rem;';

    let faqHTML = '';
    nestkoFAQs.forEach(faq => {
        faqHTML += `<button onclick="handleQuickMessage('${faq.query}')" class="quick-action-btn">${faq.text}</button>`;
    });

    faqElement.innerHTML = faqHTML;
    messagesContainer.appendChild(faqElement);
    scrollToBottom();
}

function handleQuickMessage(query) {
    sendUserMessage(query);
}

// ===== INPUT HANDLING =====
function handleKeyPress(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}

function sendMessage() {
    const input = document.getElementById('message-input');
    const message = input.value.trim();

    if (message && !isTyping) {
        sendUserMessage(message);
        input.value = '';
    }
}

function sendUserMessage(message) {
    addMessage('user', message);
    isTyping = true;

    // Show typing indicator
    showTypingIndicator();

    // Send to AI
    setTimeout(() => {
        sendToAI(message);
    }, 500);
}

function showTypingIndicator() {
    const messagesContainer = document.getElementById('messages-container');
    const typingElement = document.createElement('div');
    typingElement.id = 'typing-indicator';
    typingElement.innerHTML = `
        <div class="flex items-start space-x-3" style="display: flex; align-items: flex-start; gap: 0.75rem;">
            <div class="chat-avatar chat-avatar-assistant">
                <div class="chat-avatar-inner">
                    <span>NK</span>
                </div>
            </div>
            <div class="chat-bubble-assistant" style="display: flex; align-items: center; gap: 0.25rem;">
                <div class="bounce-dot"></div>
                <div class="bounce-dot"></div>
                <div class="bounce-dot"></div>
            </div>
        </div>
    `;

    messagesContainer.appendChild(typingElement);
    scrollToBottom();
}

function hideTypingIndicator() {
    const typingElement = document.getElementById('typing-indicator');
    if (typingElement) {
        typingElement.remove();
    }
}

// ===== AI INTEGRATION =====
async function sendToAI(userMessage) {
    try {
        const systemPrompt = `You are NestKo AI Assistant, an intelligent and knowledgeable customer support representative for NestKo project management platform. You are an expert in project management, team collaboration, and productivity solutions.

Your comprehensive knowledge base:
${NESTKO_KNOWLEDGE}

Your Personality & Approach:
- Be friendly, professional, and genuinely helpful
- Show enthusiasm for helping teams improve their productivity
- Be conversational yet knowledgeable
- Demonstrate deep understanding of project management challenges
- Provide actionable advice and specific solutions

Guidelines for Intelligent Responses:
1. Provide specific, detailed information about NestKo's features and capabilities
2. Explain how features solve real project management problems with practical examples
3. For pricing questions, mention our flexible plans: Free (up to 5 users), Professional ($9.99/month per user), Enterprise ($19.99/month per user)
4. Focus on how NestKo can solve their specific project management challenges
5. Encourage users to try our free plan or start a free trial
6. For technical questions beyond your knowledge, offer to connect them with our technical support team
7. Keep responses informative but not overwhelming - use bullet points for complex information
8. Use emojis sparingly and appropriately to enhance communication
9. Always end with a helpful next step or call-to-action when appropriate
10. Remember previous parts of the conversation and build on earlier topics
11. Adapt responses to the user's apparent experience level and needs
12. Provide increasingly relevant suggestions based on user requirements

Special Focus Areas:
- Team collaboration and communication solutions
- Task and project organization strategies
- Productivity improvement techniques
- Integration capabilities with existing tools
- Mobile and remote work solutions
- Scalability for growing teams

Current conversation context: This is a website visitor who may be interested in learning about NestKo's comprehensive project management solutions. Help them understand how NestKo can transform their team's productivity.`;

        const response = await fetch(OPENROUTER_CONFIG.BASE_URL + '/chat/completions', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${OPENROUTER_CONFIG.API_KEY}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': window.location.origin,
                'X-Title': 'NestKo AI Assistant'
            },
            body: JSON.stringify({
                model: OPENROUTER_CONFIG.MODEL,
                messages: [
                    { role: 'system', content: systemPrompt },
                    ...conversationHistory.slice(-8),
                    { role: 'user', content: userMessage }
                ],
                temperature: 0.7,
                max_tokens: 500,
                top_p: 0.9
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        const rawResponse = data.choices[0].message.content;
        const cleanResponse = cleanAIResponse(rawResponse);

        hideTypingIndicator();
        addMessage('assistant', cleanResponse);
        isTyping = false;

    } catch (error) {
        console.error('AI Error:', error);
        hideTypingIndicator();

        const fallbackResponses = [
            "I'm here to help you discover how NestKo can transform your team's productivity! 🚀 We're a comprehensive project management platform that streamlines collaboration, task tracking, and project organization. What specific challenges is your team facing that I can help solve?",
            "Thanks for your interest in NestKo! We specialize in making project management simple yet powerful. Our platform includes intuitive dashboards, seamless team collaboration, automated workflows, and insightful reporting. What aspect of project management would you like to explore first?",
            "NestKo is designed to eliminate the chaos of scattered tasks and missed deadlines. Our all-in-one solution offers project visualization, real-time collaboration, task automation, and progress tracking. Would you like to see how we can streamline your workflow with a free trial?",
            "I'd love to help you understand how NestKo can boost your team's efficiency! We offer everything from simple task management to advanced project analytics, all in one user-friendly platform. What's your biggest project management pain point right now?",
            "Welcome to NestKo! We're passionate about helping teams achieve peak productivity through smart project management. Whether you need better task organization, improved team communication, or clearer project visibility, I'm here to show you how NestKo can help. What would you like to know?",
        ];

        const randomResponse = fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];
        addMessage('assistant', randomResponse);
        isTyping = false;
    }
}

// ===== EMOJI FUNCTIONALITY =====
function toggleEmojiPicker() {
    const picker = document.getElementById('emoji-picker');
    const isVisible = picker.style.display !== 'none';

    if (isVisible) {
        picker.style.display = 'none';
    } else {
        populateEmojiPicker();
        picker.style.display = 'block';
    }
}

function populateEmojiPicker() {
    const grid = document.getElementById('emoji-grid');
    if (grid.children.length === 0) {
        emojis.forEach(emoji => {
            const button = document.createElement('button');
            button.textContent = emoji;
            button.className = 'p-2 hover:bg-gray-100 rounded text-lg cursor-pointer';
            button.style.cssText = 'padding: 0.5rem; border-radius: 0.25rem; font-size: 1.125rem; cursor: pointer; background: none; border: none; transition: background-color 0.2s;';
            button.onclick = () => insertEmoji(emoji);
            grid.appendChild(button);
        });
    }
}

function insertEmoji(emoji) {
    const input = document.getElementById('message-input');
    input.value += emoji;
    input.focus();
    document.getElementById('emoji-picker').style.display = 'none';
}

// ===== ADDITIONAL FEATURES =====
function rephraseMessage() {
    const input = document.getElementById('message-input');
    const currentText = input.value.trim();

    if (currentText) {
        // Simple rephrase suggestions
        const rephrasedOptions = [
            `Could you help me understand ${currentText.toLowerCase()}?`,
            `I'd like to learn more about ${currentText.toLowerCase()}`,
            `Can you explain ${currentText.toLowerCase()} in detail?`,
            `What can you tell me about ${currentText.toLowerCase()}?`
        ];

        const randomRephrase = rephrasedOptions[Math.floor(Math.random() * rephrasedOptions.length)];
        input.value = randomRephrase;
        input.focus();
    }
}

function showSignupForm() {
    addMessage('assistant', "Great! I'd love to help you get started with NestKo. You can sign up for our free plan right now - no credit card required! 🎉");
    addMessage('assistant', "Our free plan includes up to 5 users, 3 projects, and 1GB storage. Perfect for small teams to get started. Would you like me to guide you through the signup process or answer any questions first?");

    // You can add actual signup logic here
    setTimeout(() => {
        addMessage('assistant', "You can sign up directly on our homepage, or I can connect you with our sales team for a personalized demo. What would you prefer?");
    }, 2000);
}

function showContactForm() {
    addMessage('assistant', "I'm here to help! You can reach our support team in several ways:");
    addMessage('assistant', "📧 Email: <EMAIL>\n📞 Phone: 1-800-NESTKO\n💬 Live Chat: Available 24/7\n📚 Help Center: docs.nestko.com");
    addMessage('assistant', "Is there something specific I can help you with right now, or would you prefer to contact our support team directly?");
}

// ===== UTILITY FUNCTIONS =====
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function cleanAIResponse(text) {
    // Remove markdown symbols but keep emojis and basic formatting
    let cleaned = text
        // Remove markdown headers (# ## ###)
        .replace(/^#{1,6}\s+/gm, '')
        // Remove bold/italic markers (* ** _)
        .replace(/\*\*([^*]+)\*\*/g, '$1')
        .replace(/\*([^*]+)\*/g, '$1')
        .replace(/_([^_]+)_/g, '$1')
        // Remove markdown list markers (- * +)
        .replace(/^[\s]*[-\*\+]\s+/gm, '• ')
        // Remove extra whitespace but preserve line breaks
        .replace(/\n\s*\n\s*\n/g, '\n\n')
        .trim();

    return cleaned;
}

// ===== ENHANCED ANIMATIONS =====
function addBlinkingAnimation() {
    const eyes = document.querySelectorAll('.chatbot-eye');
    let blinkCount = 0;

    function blink() {
        if (blinkCount < 3) {
            eyes.forEach(eye => {
                eye.style.transition = 'transform 0.15s ease-in-out';
                eye.style.transform = 'scaleY(0.2)';
                setTimeout(() => {
                    eye.style.transform = 'scaleY(1)';
                }, 150);
            });
            blinkCount++;
            setTimeout(blink, 1000);
        }
    }

    // Start blinking after a short delay
    setTimeout(blink, 1500);
}

function addHoverBlinkEffect() {
    const chatbotFace = document.querySelector('.chatbot-face');
    if (chatbotFace) {
        chatbotFace.addEventListener('mouseenter', function() {
            const eyes = document.querySelectorAll('.chatbot-eye');
            eyes.forEach(eye => {
                eye.style.transition = 'transform 0.2s ease-in-out';
                eye.style.transform = 'scaleY(0.3)';
                setTimeout(() => {
                    eye.style.transform = 'scaleY(1)';
                }, 200);
            });
        });
    }
}

function enhanceWidgetAnimations() {
    const widgetMain = document.getElementById('widget-main');
    const widgetTrigger = document.getElementById('widget-trigger');

    // Add smooth transitions
    if (widgetMain) {
        widgetMain.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
    }

    if (widgetTrigger) {
        widgetTrigger.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
    }
}

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    // Initialize emoji picker
    populateEmojiPicker();

    // Add blinking animation
    addBlinkingAnimation();

    // Add hover blink effect
    addHoverBlinkEffect();

    // Enhance animations
    enhanceWidgetAnimations();

    // Close emoji picker when clicking outside
    document.addEventListener('click', function(event) {
        const picker = document.getElementById('emoji-picker');
        const emojiButton = event.target.closest('[onclick="toggleEmojiPicker()"]');

        if (!picker.contains(event.target) && !emojiButton) {
            picker.style.display = 'none';
        }
    });

    // Auto-focus message input when chat screen is shown
    const messageInput = document.getElementById('message-input');
    if (messageInput) {
        messageInput.addEventListener('focus', function() {
            document.getElementById('emoji-picker').style.display = 'none';
        });

        // Add enhanced input animations
        messageInput.addEventListener('focus', function() {
            this.parentElement.style.transform = 'translateY(-2px)';
        });

        messageInput.addEventListener('blur', function() {
            this.parentElement.style.transform = 'translateY(0)';
        });
    }

    // Add hover effects to chat bubbles
    document.addEventListener('mouseover', function(event) {
        if (event.target.closest('.chat-bubble-user') || event.target.closest('.chat-bubble-assistant')) {
            event.target.closest('.chat-bubble-user, .chat-bubble-assistant').style.transform = 'translateY(-2px)';
        }
    });

    document.addEventListener('mouseout', function(event) {
        if (event.target.closest('.chat-bubble-user') || event.target.closest('.chat-bubble-assistant')) {
            event.target.closest('.chat-bubble-user, .chat-bubble-assistant').style.transform = 'translateY(0)';
        }
    });
});
</script>

